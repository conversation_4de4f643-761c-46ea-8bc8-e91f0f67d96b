from flask import Flask, render_template, request, jsonify, send_from_directory
from openai import OpenAI
import logging
import os
from config import Config
from utils.text_processor import TextProcessor
from utils.tts_handler import TTSHandler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(Config)

# Initialize OpenAI client
openai_client = None
if app.config['OPENAI_API_KEY']:
    try:
        openai_client = OpenAI(api_key=app.config['OPENAI_API_KEY'])
        logger.info("OpenAI client initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {e}")
        openai_client = None
else:
    logger.warning("OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")

# Initialize TTS handler
tts_handler = None
if openai_client:
    tts_handler = TTSHandler(
        openai_client=openai_client,
        audio_folder=app.config['AUDIO_FOLDER'],
        voice=app.config.get('TTS_VOICE', 'alloy'),
        model=app.config.get('TTS_MODEL', 'tts-1')
    )
else:
    logger.warning("TTS handler not initialized - OpenAI client unavailable")

@app.route('/')
def index():
    """Render the main page."""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat requests to OpenAI API."""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'Message cannot be empty'}), 400
        
        if not openai_client:
            return jsonify({'error': 'OpenAI API key not configured'}), 500

        # Call OpenAI API
        logger.info(f"Sending request to OpenAI: {user_message[:50]}...")

        response = openai_client.chat.completions.create(
            model=app.config['OPENAI_MODEL'],
            messages=[
                {"role": "user", "content": user_message}
            ],
            max_tokens=app.config['MAX_TOKENS'],
            temperature=app.config['TEMPERATURE']
        )

        # Extract response text
        response_text = response.choices[0].message.content.strip()
        logger.info(f"Received response from OpenAI: {len(response_text)} characters")

        return jsonify({
            'success': True,
            'response': response_text
        })
        
    except Exception as openai_error:
        error_message = str(openai_error)

        # Handle specific OpenAI errors
        if "authentication" in error_message.lower() or "api key" in error_message.lower():
            logger.error("OpenAI authentication failed")
            return jsonify({'error': 'Invalid OpenAI API key'}), 401
        elif "rate limit" in error_message.lower():
            logger.error("OpenAI rate limit exceeded")
            return jsonify({'error': 'Rate limit exceeded. Please try again later.'}), 429
        elif "openai" in error_message.lower():
            logger.error(f"OpenAI API error: {openai_error}")
            return jsonify({'error': 'OpenAI API error. Please try again.'}), 500
        else:
            # Re-raise if it's not an OpenAI-specific error
            raise
    
    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {e}")
        return jsonify({'error': 'An unexpected error occurred'}), 500

@app.route('/api/process-text', methods=['POST'])
def process_text():
    """Process text into sentences and generate TTS audio."""
    try:
        data = request.get_json()
        text = data.get('text', '').strip()

        if not text:
            return jsonify({'error': 'Text cannot be empty'}), 400

        if not tts_handler:
            return jsonify({'error': 'TTS service not available - OpenAI client not configured'}), 500
        
        # Split text into sentences
        logger.info(f"Processing text into sentences: {len(text)} characters")
        sentences = TextProcessor.split_into_sentences(text)
        
        if not sentences:
            return jsonify({'error': 'No valid sentences found in text'}), 400
        
        logger.info(f"Found {len(sentences)} sentences")
        
        # Generate TTS audio for each sentence
        sentence_data = tts_handler.generate_sentence_audio(sentences)
        
        # Clean up old files periodically
        tts_handler.cleanup_old_files()
        
        return jsonify({
            'success': True,
            'sentences': sentence_data
        })
        
    except Exception as e:
        logger.error(f"Error processing text: {e}")
        return jsonify({'error': 'Error processing text'}), 500

@app.route('/api/audio/<filename>')
def serve_audio(filename):
    """Serve audio files."""
    try:
        return send_from_directory(app.config['AUDIO_FOLDER'], filename)
    except Exception as e:
        logger.error(f"Error serving audio file {filename}: {e}")
        return jsonify({'error': 'Audio file not found'}), 404

@app.route('/api/clear', methods=['POST'])
def clear_session():
    """Clear session data and optionally clean up audio files."""
    try:
        # Optionally clean up audio files
        cleanup = request.get_json().get('cleanup_audio', False)
        
        if cleanup:
            tts_handler.cleanup_old_files(max_files=0)  # Remove all files
        
        return jsonify({'success': True, 'message': 'Session cleared'})
        
    except Exception as e:
        logger.error(f"Error clearing session: {e}")
        return jsonify({'error': 'Error clearing session'}), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Ensure audio directory exists
    os.makedirs(app.config['AUDIO_FOLDER'], exist_ok=True)
    
    # Run the app
    app.run(
        debug=app.config.get('FLASK_ENV') == 'development',
        host='0.0.0.0',
        port=5001
    )
