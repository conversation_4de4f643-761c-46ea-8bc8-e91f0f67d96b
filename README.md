# ChatGPT Text-to-Speech Web Application

A Python web application that integrates with OpenAI's ChatGPT API and OpenAI's Text-to-Speech API to provide high-quality voice synthesis. Users can ask questions, receive responses from ChatGPT, and listen to each sentence individually with interactive play buttons using OpenAI's premium TTS voices.

## Features

- **Interactive Web Interface**: Clean, responsive UI with Bootstrap styling
- **ChatGPT Integration**: Send queries to OpenAI's GPT-3.5-turbo model
- **High-Quality TTS**: OpenAI's premium text-to-speech with multiple voice options (alloy, echo, fable, onyx, nova, shimmer)
- **Interactive Audio Controls**: Play buttons for each sentence with progress indicators
- **Play All Functionality**: Play all sentences sequentially
- **Error Handling**: Comprehensive error handling for API calls and audio generation
- **Responsive Design**: Works on desktop and mobile devices
- **Audio Management**: Automatic cleanup of old audio files

## Prerequisites

- Python 3.7 or higher
- OpenAI API key
- Internet connection for API calls and TTS generation

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   - Copy `.env.example` to `.env`
   - Add your OpenAI API key:
   ```
   OPENAI_API_KEY=your_actual_openai_api_key_here
   FLASK_SECRET_KEY=your_secret_key_here
   FLASK_ENV=development
   ```

4. **Download NLTK data** (will be done automatically on first run):
   ```python
   import nltk
   nltk.download('punkt')
   ```

## Usage

1. **Start the application**:
   ```bash
   python app.py
   ```

2. **Open your web browser** and navigate to:
   ```
   http://localhost:5000
   ```

3. **Use the application**:
   - Enter your question or prompt in the text area
   - Click "Send" to get a response from ChatGPT
   - Wait for the response to be processed and audio to be generated
   - Click individual "Play" buttons to hear specific sentences
   - Use "Play All" to hear the entire response sequentially
   - Click "Clear" to reset and start over

## API Endpoints

- `GET /` - Main application page
- `POST /api/chat` - Send message to ChatGPT
- `POST /api/process-text` - Process text into sentences and generate TTS
- `GET /api/audio/<filename>` - Serve audio files
- `POST /api/clear` - Clear session data

## Configuration

Edit `config.py` to modify:
- OpenAI model settings (model, max_tokens, temperature)
- OpenAI TTS settings:
  - **Voice**: Choose from `alloy`, `echo`, `fable`, `onyx`, `nova`, `shimmer`
  - **Model**: `tts-1` (faster) or `tts-1-hd` (higher quality)
- Audio file management settings

## File Structure

```
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── README.md            # This file
├── templates/
│   └── index.html       # Main HTML template
├── static/
│   ├── css/
│   │   └── style.css    # Custom CSS styles
│   ├── js/
│   │   └── script.js    # JavaScript functionality
│   └── audio/           # Generated audio files (temporary)
└── utils/
    ├── __init__.py
    ├── text_processor.py # Text processing utilities
    └── tts_handler.py    # Text-to-speech handling
```

## Dependencies

- **Flask**: Web framework
- **openai**: OpenAI API client for ChatGPT and TTS
- **httpx**: HTTP client for OpenAI API
- **nltk**: Natural Language Toolkit for sentence tokenization
- **python-dotenv**: Environment variable management

## Troubleshooting

1. **OpenAI API Key Issues**:
   - Ensure your API key is valid and has sufficient credits
   - Check that the key is properly set in the `.env` file

2. **Audio Generation Issues**:
   - Ensure internet connection for gTTS
   - Check that the `static/audio` directory exists and is writable

3. **NLTK Data Issues**:
   - The app will automatically download required NLTK data on first run
   - If issues persist, manually run: `python -c "import nltk; nltk.download('punkt')"`

4. **Port Already in Use**:
   - Change the port in `app.py` or kill the process using port 5000

## Security Notes

- Never commit your `.env` file with real API keys
- Use environment variables for sensitive configuration
- Consider implementing rate limiting for production use
- The application includes basic error handling but should be enhanced for production

## License

This project is for educational and demonstration purposes.
