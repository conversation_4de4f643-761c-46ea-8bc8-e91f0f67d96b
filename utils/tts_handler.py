import os
import hashlib
import logging
from openai import OpenAI
from utils.text_processor import TextProcessor
import tempfile

logger = logging.getLogger(__name__)

class TTSHandler:
    """Handles text-to-speech conversion using OpenAI Text-to-Speech."""

    def __init__(self, openai_client, audio_folder='static/audio', voice='alloy', model='tts-1'):
        self.openai_client = openai_client
        self.audio_folder = audio_folder
        self.voice = voice  # Available voices: alloy, echo, fable, onyx, nova, shimmer
        self.model = model  # Available models: tts-1, tts-1-hd
        self.ensure_audio_folder()
    
    def ensure_audio_folder(self):
        """Create audio folder if it doesn't exist."""
        if not os.path.exists(self.audio_folder):
            os.makedirs(self.audio_folder)
    
    def generate_filename(self, text):
        """
        Generate a unique filename based on text content.
        
        Args:
            text (str): Input text
            
        Returns:
            str: Generated filename
        """
        # Create hash of the text for unique filename
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"tts_{text_hash}.mp3"
    
    def text_to_speech(self, text):
        """
        Convert text to speech using OpenAI TTS and save as audio file.

        Args:
            text (str): Text to convert to speech

        Returns:
            str: Path to generated audio file, or None if failed
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for TTS conversion")
            return None

        if not self.openai_client:
            logger.error("OpenAI client not available for TTS")
            return None

        try:
            # Validate and clean text for TTS
            cleaned_text = TextProcessor.validate_text_for_tts(text)

            if not cleaned_text:
                logger.warning("Text became empty after cleaning")
                return None

            # Generate filename
            filename = self.generate_filename(cleaned_text)
            filepath = os.path.join(self.audio_folder, filename)

            # Check if file already exists
            if os.path.exists(filepath):
                logger.info(f"Audio file already exists: {filename}")
                return filename

            # Generate TTS using OpenAI
            logger.info(f"Generating TTS with OpenAI for text: {cleaned_text[:50]}...")

            response = self.openai_client.audio.speech.create(
                model=self.model,
                voice=self.voice,
                input=cleaned_text,
                response_format="mp3"
            )

            # Save to file
            with open(filepath, 'wb') as f:
                f.write(response.content)

            logger.info(f"Generated OpenAI TTS audio: {filename}")
            return filename

        except Exception as e:
            logger.error(f"Error generating OpenAI TTS for text '{text[:50]}...': {e}")
            return None
    
    def generate_sentence_audio(self, sentences):
        """
        Generate audio files for a list of sentences.
        
        Args:
            sentences (list): List of sentences
            
        Returns:
            list: List of dictionaries with sentence text and audio filename
        """
        results = []
        
        for i, sentence in enumerate(sentences):
            if not sentence.strip():
                continue
                
            audio_filename = self.text_to_speech(sentence)
            
            results.append({
                'id': i,
                'text': sentence,
                'audio_file': audio_filename,
                'has_audio': audio_filename is not None
            })
        
        return results
    
    def cleanup_old_files(self, max_files=100):
        """
        Clean up old audio files to prevent storage issues.
        
        Args:
            max_files (int): Maximum number of files to keep
        """
        try:
            if not os.path.exists(self.audio_folder):
                return
            
            files = []
            for filename in os.listdir(self.audio_folder):
                if filename.startswith('tts_') and filename.endswith('.mp3'):
                    filepath = os.path.join(self.audio_folder, filename)
                    files.append((filepath, os.path.getctime(filepath)))
            
            # Sort by creation time (oldest first)
            files.sort(key=lambda x: x[1])
            
            # Remove oldest files if we exceed max_files
            if len(files) > max_files:
                files_to_remove = files[:-max_files]
                for filepath, _ in files_to_remove:
                    try:
                        os.remove(filepath)
                        logger.info(f"Removed old audio file: {os.path.basename(filepath)}")
                    except Exception as e:
                        logger.error(f"Error removing file {filepath}: {e}")
                        
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
