import re
import nltk
from nltk.tokenize import sent_tokenize
import logging

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

logger = logging.getLogger(__name__)

class TextProcessor:
    """Handles text processing operations including sentence splitting."""
    
    @staticmethod
    def split_into_sentences(text):
        """
        Split text into sentences using NLTK's sentence tokenizer.
        
        Args:
            text (str): Input text to split
            
        Returns:
            list: List of sentences
        """
        if not text or not text.strip():
            return []
        
        try:
            # Clean the text first
            cleaned_text = TextProcessor.clean_text(text)
            
            # Use NLTK's sentence tokenizer
            sentences = sent_tokenize(cleaned_text)
            
            # Filter out very short sentences (likely fragments)
            filtered_sentences = [
                sentence.strip() 
                for sentence in sentences 
                if len(sentence.strip()) > 3
            ]
            
            return filtered_sentences
            
        except Exception as e:
            logger.error(f"Error splitting text into sentences: {e}")
            # Fallback to simple splitting
            return TextProcessor.simple_sentence_split(text)
    
    @staticmethod
    def clean_text(text):
        """
        Clean text by removing excessive whitespace and formatting issues.
        
        Args:
            text (str): Input text to clean
            
        Returns:
            str: Cleaned text
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove leading/trailing whitespace
        text = text.strip()
        
        return text
    
    @staticmethod
    def simple_sentence_split(text):
        """
        Fallback method for sentence splitting using regex.
        
        Args:
            text (str): Input text to split
            
        Returns:
            list: List of sentences
        """
        # Simple regex-based sentence splitting
        sentences = re.split(r'[.!?]+', text)
        
        # Clean and filter sentences
        filtered_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 3:
                # Add period if missing
                if not sentence.endswith(('.', '!', '?')):
                    sentence += '.'
                filtered_sentences.append(sentence)
        
        return filtered_sentences
    
    @staticmethod
    def validate_text_for_tts(text):
        """
        Validate and prepare text for text-to-speech conversion.
        
        Args:
            text (str): Input text to validate
            
        Returns:
            str: Validated and prepared text
        """
        if not text:
            return ""
        
        # Remove or replace problematic characters
        text = re.sub(r'[^\w\s.,!?;:\-\'"()]', ' ', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
