<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT Text-to-Speech App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <header class="text-center py-4">
                    <h1 class="display-4 text-primary">
                        <i class="fas fa-comments"></i> ChatGPT Text-to-Speech
                    </h1>
                    <p class="lead text-muted">Ask <PERSON>t<PERSON>T a question and listen to the response sentence by sentence</p>
                </header>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <!-- Input Section -->
                <div class="card mb-4 shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-keyboard"></i> Ask ChatGPT</h5>
                    </div>
                    <div class="card-body">
                        <form id="chatForm">
                            <div class="mb-3">
                                <textarea 
                                    id="userInput" 
                                    class="form-control" 
                                    rows="4" 
                                    placeholder="Enter your question or prompt here..."
                                    required
                                ></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" id="submitBtn" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Send
                                </button>
                                <button type="button" id="clearBtn" class="btn btn-outline-secondary">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Loading Section -->
                <div id="loadingSection" class="text-center mb-4" style="display: none;">
                    <div class="card shadow">
                        <div class="card-body">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 mb-0" id="loadingText">Sending request to ChatGPT...</p>
                        </div>
                    </div>
                </div>

                <!-- Error Section -->
                <div id="errorSection" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorMessage"></span>
                </div>

                <!-- Response Section -->
                <div id="responseSection" style="display: none;">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-robot"></i> ChatGPT Response</h5>
                            <div>
                                <button id="playAllBtn" class="btn btn-light btn-sm me-2">
                                    <i class="fas fa-play"></i> Play All
                                </button>
                                <button id="stopAllBtn" class="btn btn-light btn-sm" style="display: none;">
                                    <i class="fas fa-stop"></i> Stop All
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="sentencesContainer">
                                <!-- Sentences will be dynamically added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio elements will be dynamically created -->
    <div id="audioContainer" style="display: none;"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>
