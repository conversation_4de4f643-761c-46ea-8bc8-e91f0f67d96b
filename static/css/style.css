/* Custom styles for ChatGPT TTS App */

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container-fluid {
    padding: 20px;
}

/* Header styles */
header h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    color: white !important;
}

header p {
    color: rgba(255,255,255,0.8) !important;
}

/* Card styles */
.card {
    border: none;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

/* Form styles */
#userInput {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

#userInput:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Button styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Sentence styles */
.sentence-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.sentence-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sentence-text {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 10px;
    color: #333;
}

.sentence-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.play-btn {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
    transform: scale(1.05);
}

.play-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.play-btn.playing {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
}

.play-btn.playing:hover {
    background: linear-gradient(45deg, #c82333, #e8650e);
}

/* Audio progress indicator */
.audio-progress {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.audio-progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.1s ease;
}

/* Loading styles */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

#loadingText {
    color: #6c757d;
    font-weight: 500;
}

/* Error styles */
.alert-danger {
    border-radius: 10px;
    border: none;
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .sentence-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .play-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Animation for sentence appearance */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.sentence-item {
    animation: slideInUp 0.5s ease forwards;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}
