// ChatGPT TTS App JavaScript

class ChatGPTTTSApp {
    constructor() {
        this.currentAudio = null;
        this.audioElements = new Map();
        this.isPlayingAll = false;
        this.playAllQueue = [];
        this.currentPlayAllIndex = 0;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Form submission
        document.getElementById('chatForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // Clear button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAll();
        });

        // Play all button
        document.getElementById('playAllBtn').addEventListener('click', () => {
            this.playAll();
        });

        // Stop all button
        document.getElementById('stopAllBtn').addEventListener('click', () => {
            this.stopAll();
        });
    }

    async handleSubmit() {
        const userInput = document.getElementById('userInput').value.trim();
        
        if (!userInput) {
            this.showError('Please enter a message');
            return;
        }

        this.showLoading('Sending request to ChatGPT...');
        this.hideError();
        this.hideResponse();

        try {
            // Send request to ChatGPT
            const chatResponse = await this.sendChatRequest(userInput);
            
            if (!chatResponse.success) {
                throw new Error(chatResponse.error || 'Failed to get response from ChatGPT');
            }

            this.showLoading('Processing response and generating audio...');

            // Process text and generate TTS
            const processResponse = await this.processText(chatResponse.response);
            
            if (!processResponse.success) {
                throw new Error(processResponse.error || 'Failed to process text');
            }

            this.hideLoading();
            this.displaySentences(processResponse.sentences);

        } catch (error) {
            console.error('Error:', error);
            this.hideLoading();
            this.showError(error.message || 'An unexpected error occurred');
        }
    }

    async sendChatRequest(message) {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });

        return await response.json();
    }

    async processText(text) {
        const response = await fetch('/api/process-text', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: text })
        });

        return await response.json();
    }

    displaySentences(sentences) {
        const container = document.getElementById('sentencesContainer');
        container.innerHTML = '';

        sentences.forEach((sentence, index) => {
            const sentenceElement = this.createSentenceElement(sentence, index);
            container.appendChild(sentenceElement);
            
            // Add animation delay
            setTimeout(() => {
                sentenceElement.style.opacity = '1';
                sentenceElement.style.transform = 'translateY(0)';
            }, index * 100);
        });

        this.showResponse();
    }

    createSentenceElement(sentence, index) {
        const div = document.createElement('div');
        div.className = 'sentence-item';
        div.style.opacity = '0';
        div.style.transform = 'translateY(30px)';
        div.style.transition = 'all 0.5s ease';

        const textDiv = document.createElement('div');
        textDiv.className = 'sentence-text';
        textDiv.textContent = sentence.text;

        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'sentence-controls';

        const playBtn = document.createElement('button');
        playBtn.className = 'btn play-btn';
        playBtn.innerHTML = '<i class="fas fa-play"></i> Play';
        playBtn.disabled = !sentence.has_audio;

        if (sentence.has_audio) {
            playBtn.addEventListener('click', () => {
                this.togglePlaySentence(sentence, playBtn, index);
            });
        } else {
            playBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Audio Error';
            playBtn.className = 'btn btn-secondary';
        }

        const progressDiv = document.createElement('div');
        progressDiv.className = 'audio-progress';
        progressDiv.style.display = 'none';

        const progressBar = document.createElement('div');
        progressBar.className = 'audio-progress-bar';
        progressDiv.appendChild(progressBar);

        controlsDiv.appendChild(playBtn);
        div.appendChild(textDiv);
        div.appendChild(controlsDiv);
        div.appendChild(progressDiv);

        return div;
    }

    async togglePlaySentence(sentence, button, index) {
        if (!sentence.has_audio) return;

        // Stop current audio if playing
        if (this.currentAudio && !this.currentAudio.paused) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
            this.updatePlayButton(this.currentAudio.dataset.button, false);
        }

        // Get or create audio element
        let audio = this.audioElements.get(sentence.audio_file);
        if (!audio) {
            audio = new Audio(`/api/audio/${sentence.audio_file}`);
            audio.dataset.button = button;
            this.audioElements.set(sentence.audio_file, audio);

            // Add event listeners
            audio.addEventListener('ended', () => {
                this.updatePlayButton(button, false);
                this.hideProgress(button);
                this.currentAudio = null;
                
                // Continue play all if active
                if (this.isPlayingAll) {
                    this.playNextInQueue();
                }
            });

            audio.addEventListener('error', () => {
                this.showError('Error playing audio');
                this.updatePlayButton(button, false);
                this.hideProgress(button);
            });

            audio.addEventListener('timeupdate', () => {
                this.updateProgress(button, audio);
            });
        }

        // Toggle play/pause
        if (audio.paused) {
            try {
                await audio.play();
                this.currentAudio = audio;
                this.updatePlayButton(button, true);
                this.showProgress(button);
            } catch (error) {
                console.error('Error playing audio:', error);
                this.showError('Error playing audio');
            }
        } else {
            audio.pause();
            this.updatePlayButton(button, false);
            this.hideProgress(button);
            this.currentAudio = null;
        }
    }

    updatePlayButton(button, isPlaying) {
        if (isPlaying) {
            button.innerHTML = '<i class="fas fa-pause"></i> Pause';
            button.classList.add('playing');
        } else {
            button.innerHTML = '<i class="fas fa-play"></i> Play';
            button.classList.remove('playing');
        }
    }

    showProgress(button) {
        const progressDiv = button.parentElement.nextElementSibling;
        progressDiv.style.display = 'block';
    }

    hideProgress(button) {
        const progressDiv = button.parentElement.nextElementSibling;
        progressDiv.style.display = 'none';
        const progressBar = progressDiv.querySelector('.audio-progress-bar');
        progressBar.style.width = '0%';
    }

    updateProgress(button, audio) {
        const progressDiv = button.parentElement.nextElementSibling;
        const progressBar = progressDiv.querySelector('.audio-progress-bar');
        
        if (audio.duration > 0) {
            const progress = (audio.currentTime / audio.duration) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    playAll() {
        const playButtons = document.querySelectorAll('.play-btn:not(:disabled)');
        if (playButtons.length === 0) return;

        this.isPlayingAll = true;
        this.playAllQueue = Array.from(playButtons);
        this.currentPlayAllIndex = 0;

        document.getElementById('playAllBtn').style.display = 'none';
        document.getElementById('stopAllBtn').style.display = 'inline-block';

        this.playNextInQueue();
    }

    playNextInQueue() {
        if (!this.isPlayingAll || this.currentPlayAllIndex >= this.playAllQueue.length) {
            this.stopAll();
            return;
        }

        const button = this.playAllQueue[this.currentPlayAllIndex];
        this.currentPlayAllIndex++;
        
        // Trigger click on the button
        button.click();
    }

    stopAll() {
        this.isPlayingAll = false;
        this.playAllQueue = [];
        this.currentPlayAllIndex = 0;

        if (this.currentAudio && !this.currentAudio.paused) {
            this.currentAudio.pause();
            this.currentAudio.currentTime = 0;
            this.updatePlayButton(this.currentAudio.dataset.button, false);
            this.hideProgress(this.currentAudio.dataset.button);
        }

        document.getElementById('playAllBtn').style.display = 'inline-block';
        document.getElementById('stopAllBtn').style.display = 'none';
    }

    clearAll() {
        // Stop any playing audio
        this.stopAll();

        // Clear form
        document.getElementById('userInput').value = '';

        // Hide sections
        this.hideLoading();
        this.hideError();
        this.hideResponse();

        // Clear audio elements
        this.audioElements.clear();
        this.currentAudio = null;

        // Optional: Clear audio files on server
        fetch('/api/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ cleanup_audio: false })
        }).catch(console.error);
    }

    showLoading(message) {
        document.getElementById('loadingText').textContent = message;
        document.getElementById('loadingSection').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingSection').style.display = 'none';
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorSection').style.display = 'block';
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';
    }

    showResponse() {
        document.getElementById('responseSection').style.display = 'block';
    }

    hideResponse() {
        document.getElementById('responseSection').style.display = 'none';
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChatGPTTTSApp();
});
